'use client'

/**
 * 尊享之旅页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface JourneyContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
}

// 区块组件
const BlockRenderer = ({
  block,
  index,
}: {
  block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] }
  index: number
}) => {
  const { __component, title, description, media } = block

  // 判断是否为奇数块（从1开始计数）
  const isOdd = (index + 1) % 2 === 1

  // 判断文字颜色：模块1、3、6、8为黑色，其他为白色
  const blockNumber = index + 1
  const isBlackText = [1, 3, 6, 8].includes(blockNumber)
  const textColor = isBlackText ? 'text-black' : 'text-white'

  // 根据不同的组件类型渲染不同的布局
  switch (__component) {
    case 'shared.block':
      return (
        <motion.section
          className="w-full overflow-hidden"
          initial={{ opacity: 0, y: 60 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true, amount: 0.3 }}
        >

          {/* 内容区域 */}
          <div
            className="relative z-10 w-full"
            style={{
              backgroundImage: `url(${media?.url})`,
              backgroundPosition: isOdd ? 'left center' : 'right center',
              backgroundSize: 'cover',
            }}
          >
            {/* PC端布局 */}
            <div className="hidden lg:block">
              <div className="mx-auto w-[76%] py-20">
                <motion.div
                  className={`flex min-h-[400px] items-center ${
                    isOdd ? 'justify-end' : 'justify-start'
                  }`}
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true, amount: 0.3 }}
                >
                  <div className={`w-1/2  ${isOdd ? 'pl-12' : 'pr-12'}`}>
                    <motion.h2
                      className={`mb-8 text-[22px] font-[500] font-bold ${textColor}`}
                      variants={fadeInUp}
                      transition={{ duration: 0.6 }}
                    >
                      {title}
                    </motion.h2>
                    <motion.div
                      className={`text-sm text-justify font-[300] leading-[28px] ${textColor}`}
                      variants={fadeInUp}
                      transition={{ duration: 0.6, delay: 0.2 }}
                    >
                      {description}
                    </motion.div>
                  </div>
                </motion.div>
              </div>
            </div>

            {/* 移动端布局 */}
            <div
              className="block lg:hidden positive"
              style={{
                backgroundImage: `url(${media?.url})`,
                backgroundPosition: isOdd ? 'left center' : 'right center',
                backgroundSize: 'cover',
              }}
            >
              <div className="px-7 mb-8 h-72 pb-6" style={{
                background: 'linear-gradient(to top, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0))',
              }}>
                <motion.div
                  className={`flex flex-col items-center text-left text-white `}
                  variants={staggerContainer}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true, amount: 0.3 }}
                >
                  <motion.h2
                    className={`w-full mb-6 text-[18px] font-[500] mt-36 text-left `}
                    variants={fadeInUp}
                    transition={{ duration: 0.6 }}
                  >
                    {title}
                  </motion.h2>
                  <motion.div
                    className={`text-sm font-[300] text-base leading-[28px] line-clamp-responsive`}
                    variants={fadeInUp}
                    transition={{ duration: 0.6, delay: 0.2 }}
                  >
                    {description}
                  </motion.div>
                </motion.div>
              </div>
              
            </div>
          </div>
        </motion.section>
      )

    default:
      return null
  }
}

// 主要的 JourneyContent 组件
export default function JourneyContent({ pageData }: JourneyContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-hidden overflow-y-visible bg-white lg:bg-[#000] pt-16 lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="overflow-x-hidden overflow-y-visible"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-10 left-7.5 z-10 w-full text-[#fff] lg:top-[40%] lg:left-[12%]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm lg:block lg:text-justify font-[300] leading-[28px]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="w-full">
          {pageData.blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, amount: 0.2 }}
              >
                <BlockRenderer block={block} index={index} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
