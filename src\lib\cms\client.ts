/**
 * 通用Strapi API客户端
 */

import type {
  StrapiResponse,
  StrapiSingleResponse,
  StrapiQueryParams,
  StrapiErrorResponse,
  StrapiLocale
} from '@/types/strapi'
import { getStrapiUrl, getStrapiToken } from './config'

// Next.js locale到Strapi locale的映射
const LOCALE_MAP: Record<string, StrapiLocale> = {
  'en-US': 'en',
  'zh-CN': 'zh-CN'
} as const

/**
 * 递归构建嵌套对象的查询参数
 */
function buildNestedParams(
  obj: Record<string, unknown>,
  prefix: string,
  searchParams: URLSearchParams
): void {
  Object.entries(obj).forEach(([key, value]) => {
    const paramKey = `${prefix}[${key}]`

    if (value && typeof value === 'object' && !Array.isArray(value)) {
      // 递归处理嵌套对象
      buildNestedParams(value as Record<string, unknown>, paramKey, searchParams)
    } else if (Array.isArray(value)) {
      // 处理数组
      value.forEach(item => {
        searchParams.append(paramKey, String(item))
      })
    } else if (value !== undefined && value !== null) {
      // 处理基本类型
      searchParams.append(paramKey, String(value))
    }
  })
}

/**
 * 构建查询字符串
 */
function buildQueryString(params: StrapiQueryParams): string {
  const searchParams = new URLSearchParams()

  // 处理locale
  if (params.locale) {
    searchParams.append('locale', params.locale)
  }

  // 处理populate
  if (params.populate) {
    if (typeof params.populate === 'string') {
      searchParams.append('populate', params.populate)
    } else if (Array.isArray(params.populate)) {
      params.populate.forEach(item => {
        searchParams.append('populate', item)
      })
    } else {
      // 处理复杂的populate对象
      buildNestedParams(params.populate, 'populate', searchParams)
    }
  }

  // 处理filters - 支持深层嵌套
  if (params.filters) {
    buildNestedParams(params.filters, 'filters', searchParams)
  }

  // 处理sort
  if (params.sort) {
    if (Array.isArray(params.sort)) {
      params.sort.forEach(item => {
        searchParams.append('sort', item)
      })
    } else {
      searchParams.append('sort', params.sort)
    }
  }

  // 处理pagination
  if (params.pagination) {
    Object.entries(params.pagination).forEach(([key, value]) => {
      if (value !== undefined) {
        searchParams.append(`pagination[${key}]`, String(value))
      }
    })
  }

  // 处理publicationState
  if (params.publicationState) {
    searchParams.append('publicationState', params.publicationState)
  }

  // 处理其他顶级对象属性（如 heroes, blocks 等）
  // 这些属性应该作为 populate 的子属性处理
  const standardParams = ['locale', 'populate', 'filters', 'sort', 'pagination', 'publicationState']
  Object.entries(params).forEach(([key, value]) => {
    if (!standardParams.includes(key) && value && typeof value === 'object' && !Array.isArray(value)) {
      buildNestedParams(value as Record<string, unknown>, `populate[${key}]`, searchParams)
    }
  })

  return searchParams.toString()
}

/**
 * 发送API请求
 */
async function fetchFromStrapi<T>(
  endpoint: string, 
  params: StrapiQueryParams = {}
): Promise<T> {
  const baseUrl = getStrapiUrl()
  const token = getStrapiToken()
  
  const queryString = buildQueryString(params)
  const url = `${baseUrl}${endpoint}${queryString ? `?${queryString}` : ''}`

  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  }

  if (token) {
    headers.Authorization = `Bearer ${token}`
  }

  try {
    const response = await fetch(url, {
      headers,
      cache: 'no-store' // 禁用缓存，实时获取数据
    })
    if (!response.ok) {
      const errorData: StrapiErrorResponse = await response.json()
      throw new Error(
        `Strapi API Error: ${errorData.error?.message || response.statusText}`
      )
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Strapi API request failed:', error)
    throw error
  }
}

/**
 * 获取多个实体
 */
export const fetchStrapiCollection = async <T>(
  endpoint: string,
  locale?: string,
  params: Omit<StrapiQueryParams, 'locale'> = {}
): Promise<StrapiResponse<T>> => {
  const strapiLocale = locale ? LOCALE_MAP[locale] || 'en' : 'en'

  return fetchFromStrapi<StrapiResponse<T>>(endpoint, {
    ...params,
    locale: strapiLocale
  })
}

/**
 * 获取单个实体
 */
export const fetchStrapiSingle = async <T>(
  endpoint: string,
  locale?: string,
  params: Omit<StrapiQueryParams, 'locale'> = {}
): Promise<StrapiSingleResponse<T>> => {
  const strapiLocale = locale ? LOCALE_MAP[locale] || 'en' : 'en'

  return fetchFromStrapi<StrapiSingleResponse<T>>(endpoint, {
    ...params,
    locale: strapiLocale
  })
}


