'use client'

/**
 * 授权医生和技师页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface DoctorsAndTechniciansContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
}

// 主要的 DoctorsAndTechniciansContent 组件
export default function DoctorsAndTechniciansContent({ pageData }: DoctorsAndTechniciansContentProps) {
  console.log(pageData)
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }
  const { blocks } = pageData
  return (
    <div className="overflow-x-hidden overflow-y-visible bg-white pt-16 lg:bg-[#000] lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="overflow-x-hidden overflow-y-visible"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-10 left-7.5 z-10 w-full text-black lg:top-[40%] lg:left-[12%]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm lg:block lg:text-justify"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="w-full">
          {blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, amount: 0.2 }}
              >
                <Image src={block.media?.url || ''} alt={block.title || ''} width={1200} height={800} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
