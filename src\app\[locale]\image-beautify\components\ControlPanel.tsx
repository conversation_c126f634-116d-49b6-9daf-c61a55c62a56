/**
 * 控制面板组件 - 分子组件
 */

'use client'

import { motion } from 'motion/react'
import Image from 'next/image'
import Button from '@/components/ui/Button'
import { useTranslation } from '@/lib/i18n/client'
import { useImageBeautifyStatus, useHasImage } from '@/lib/stores/image-beautify'
import type { ControlPanelProps } from '@/types/image-beautify'

export default function ControlPanel({
  onTeethWhitening,
  onSelectTeethIntensity,
  showTeethOptions,
  selectedTeethIntensity,
  onBeautyEnhancement,
  processingStatus,
  hasImage,
  onToggleCompare,
  isCompareMode = false,
  showCompareButton = false,
  className = ''
}: Omit<ControlPanelProps, 'onDownload' | 'hasProcessedImage'>) {
  const { t } = useTranslation()
  const storeStatus = useImageBeautifyStatus()
  const storeHasImage = useHasImage()

  // 优先使用 store 的状态，如果没有则使用 props
  const currentStatus = processingStatus || storeStatus
  const currentHasImage = hasImage ?? storeHasImage

  const isProcessing = currentStatus === 'processing'
  const canProcess = currentHasImage && !isProcessing

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`
        bg-black/0 p-2
        ${className}
      `}
    >
      <div className="max-w-4xl mx-auto">
        {/* 右侧：对比按钮 */}
        {showCompareButton && (
          <div className='flex justify-end mb-4'>
            <button
              onClick={onToggleCompare}
              className={`
                flex items-center justify-center w-10 h-10 rounded-lg transition-colors cursor-pointer
                ${isCompareMode
                  ? 'bg-brand-gold text-white'
                  : 'bg-white/10 text-white hover:bg-white/20'
                }
              `}
              aria-label={isCompareMode ? t('imageEditor.compare.exit', '退出对比模式') : t('imageEditor.compare.enter', '进入对比模式')}
            >
              <div className="icon iconfont icon-duibi" />
            </button>
          </div>
        )}
        {/* 牙齿美白选项（在按钮上方，无遮罩、无标题、无关闭） */}
        {showTeethOptions && (
          <div className="mb-4">
            <div className="grid grid-cols-6 gap-3">
              {[
                { value: 100, label: 'A1' },
                { value: 80, label: 'A2' },
                { value: 60, label: 'B1' },
                { value: 40, label: 'B2' },
                { value: 20, label: 'C1' },
                { value: 10, label: 'C2' },
              ].map((opt) => {
                const isActive = selectedTeethIntensity === opt.value
                return (
                  <button
                    key={opt.value}
                    onClick={() => onSelectTeethIntensity(opt.value)}
                    className={`
                      transition-all duration-200 rounded-lg p-2 cursor-pointer
                      ${isActive
                        ? 'bg-brand-gold/30'
                        : 'bg-white/5 hover:bg-white/10'
                      }
                    `}
                  >
                    <Image
                      src={`/images/beautify-tooth-${opt.value}.png`}
                      alt={opt.label}
                      width={56}
                      height={56}
                      className={`
                        w-14 h-14 object-contain mx-auto transition-all duration-200
                        ${isActive ? '' : 'grayscale-[0.3]'}
                      `}
                    />
                    <div className={`
                      text-center text-xs transition-colors duration-200
                      ${isActive ? 'text-brand-gold' : 'text-gray-300'}
                    `}>
                      {opt.label}
                    </div>
                  </button>
                )
              })}
            </div>
          </div>
        )}

        {/* 主要内容区域 */}
        <div className="flex flex-col flex-row items-center justify-center gap-2">
          {/* 牙齿美白按钮 */}
          <Button
            onClick={onTeethWhitening}
            loading={isProcessing}
            disabled={!canProcess}
            variant="primary"
            className="w-full lg:w-auto min-w-[200px]"
          >
            <span className="icon iconfont icon-meibaiyachi pr-2" />
            {t('imageEditor.processing.teethWhitening', '牙齿美白')}
          </Button>

          {/* 一键美颜按钮 */}
          <Button
            onClick={onBeautyEnhancement}
            loading={isProcessing}
            disabled={!canProcess}
            variant="primary"
            className="w-full lg:w-auto min-w-[200px]"
          >
            <span className="icon iconfont icon-yijianmeiyan1 pr-2" />
            {t('imageEditor.processing.beautyEnhancement', '一键美颜')}
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
