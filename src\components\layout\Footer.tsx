'use client'
import { useState } from 'react'
import { motion } from 'motion/react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { useTranslation } from '@/lib/i18n/client'
import type { IFooter, ISite } from '@/types/strapi'

interface FooterProps {
  data: IFooter | null
  siteData: ISite | null
}

export default function Footer({ data, siteData }: FooterProps) {
  const { dictionary, locale } = useTranslation()
  const [siteAttrData, setSiteAttrData] = useState(siteData || null)
  const [footerData, setFooterData] = useState(data || null)
  const [expandedMenus, setExpandedMenus] = useState<{ [key: number]: boolean }>({})
  // console.log('footerData', data)
  // console.log('siteData', siteData)
  const toggleMenu = (menuId: number) => {
    setExpandedMenus((prev) => ({
      ...prev,
      [menuId]: !prev[menuId],
    }))
  }

  return (
    <footer className="bg-black text-white">
      <div className="w-full lg:w-[76%] mx-auto pb-4 sm:px-6 lg:px-0">
        {/* PC端布局 */}
        <div className="hidden pt-15 lg:block">
          {/* 上部分：Logo + 标题 + 描述 + 按钮 */}
          <div className="mb-15 flex items-center justify-between">
            {/* 左侧：Logo + 标题 + 描述 */}
            <div className="flex-1">
              {siteAttrData?.logo && (
                <Image
                  src={siteAttrData.logo.url}
                  alt="Lumii Logo"
                  width={120}
                  height={40}
                  className="mb-12 h-8 w-auto"
                />
              )}
              <h2 className="mb-1 text-[34px]">{siteAttrData?.title}</h2>
              <p className="text-[16px] font-[300] text-white">{siteAttrData?.slogan}</p>
            </div>

            {/* 右侧：免费AI变美按钮 */}
            <div className="flex-shrink-0">
              {footerData?.buttons && footerData.buttons.length > 0 && (
                <div className="font-size-16 bg-white px-25 py-4 text-black">
                  {footerData.buttons[0].label}
                </div>
              )}
            </div>
          </div>

          {/* 分隔线 */}
          <div className="mb-15 border-t border-[#666]"></div>

          {/* 中间部分：联系中心 + 关于lumii + 社交媒体 */}
          <div className="grid grid-cols-3 gap-16 pb-8">
            {/* 左侧：联系中心 */}
            <div>
              {data?.menus.find(
                (menu) => menu.label === '联系中心' || menu.label === 'Contact Centre'
              ) && (
                <div>
                  <h4 className="mb-6 text-[16px] font-medium">
                    {
                      data.menus.find(
                        (menu) => menu.label === '联系中心' || menu.label === 'Contact Centre'
                      )?.label
                    }
                  </h4>
                  <div className="mb-8 space-y-3 text-sm font-[300] text-white">
                    {siteAttrData?.phone && (
                      <p>
                        {locale === 'zh-CN' ? '免费热线：' : 'Phone: '}
                        {siteAttrData.phone}
                      </p>
                    )}
                    {siteAttrData?.email && (
                      <p>
                        {locale === 'zh-CN' ? '邮箱：' : 'Email: '}
                        {siteAttrData.email}
                      </p>
                    )}
                  </div>

                  {/* 二维码 */}
                  {footerData?.qrCodes && footerData.qrCodes.length > 0 && (
                    <div className="flex space-x-6">
                      {footerData.qrCodes.map((qrCode) => (
                        <div key={qrCode.id} className="text-center">
                          {qrCode.image && (
                            <Image
                              src={qrCode.image.url}
                              alt={qrCode.label}
                              width={98}
                              height={98}
                              className="mb-2"
                            />
                          )}
                          <p className="text-xs font-[300] text-white">{qrCode.label}</p>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 中间：关于lumii */}
            <div>
              {data?.menus.find(
                (menu) => menu.label === '关于lumii' || menu.label === 'About Lumii'
              ) && (
                <div>
                  <h4 className="mb-6 text-lg font-medium">
                    {
                      data.menus.find(
                        (menu) => menu.label === '关于lumii' || menu.label === 'About Lumii'
                      )?.label
                    }
                  </h4>
                  <ul className="space-y-3 text-sm text-gray-400">
                    {data.menus
                      .find((menu) => menu.label === '关于lumii' || menu.label === 'About Lumii')
                      ?.menus?.map((item: { id?: number; label?: string; url?: string }, itemIndex: number) => (
                        <li key={itemIndex}>
                          <Link
                            href={item.url || '#'}
                            className="transition-colors hover:text-white"
                          >
                            {item.label}
                          </Link>
                        </li>
                      ))}
                  </ul>
                </div>
              )}
            </div>

            {/* 右侧：社交媒体图标 */}
            <div className="flex justify-end">
              {footerData?.socials && footerData.socials.length > 0 && (
                <>
                  {footerData.socials.map((social) => (
                    <Link
                      key={social.id}
                      href={social.url || '#'}
                      className="ml-10 transition-opacity hover:opacity-80"
                    >
                      {social.icon && (
                        <Image
                          src={social.icon.url}
                          alt=""
                          width={0}
                          height={0}
                          sizes="100vw"
                          className="h-6 w-auto"
                        />
                      )}
                    </Link>
                  ))}
                </>
              )}
            </div>
          </div>

        </div>

        {/* 移动端布局 */}
        <div className="pt-8 lg:hidden">
          {/* Logo + 标题 + 描述 */}
          <div className="mb-6 px-4">
            {siteAttrData?.logo && (
              <Image
                src={siteAttrData.logo.url}
                alt="Lumii Logo"
                width={120}
                height={40}
                className="mb-8 h-8 w-auto"
              />
            )}
            <h2 className="mb-1 text-[24px]">{siteAttrData?.title}</h2>
            <p className="mb-5 text-sm font-[300] text-white">{siteAttrData?.slogan}</p>

            {/* 免费AI变美按钮 */}
            {footerData?.buttons && footerData.buttons.length > 0 && (
              <div className="w-full bg-white px-6 py-4 text-center text-sm text-black hover:bg-gray-200">
                {footerData.buttons[0].label}
              </div>
            )}
          </div>

          {/* 分隔线 */}
          <div className="mb-2 border-t border-[#666]"></div>
          {/* 可折叠菜单 */}
          {data?.menus.map((menu) => (
            <div key={menu.id} className="px-4">
              <button
                onClick={() => toggleMenu(menu.id)}
                className="flex w-full items-center justify-between py-4 text-left"
              >
                <h4 className="text-lg font-medium">{menu.label}</h4>
                {expandedMenus[menu.id] ? (
                  <ChevronUp className="h-5 w-5" />
                ) : (
                  <ChevronDown className="h-5 w-5" />
                )}
              </button>

              {expandedMenus[menu.id] && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="pb-4"
                >
                  {/* 联系中心内容 */}
                  {(menu.label === '联系中心' || menu.label === 'Contact Centre') && (
                    <div className="space-y-4 text-sm font-[300] text-white">
                      {siteAttrData?.phone && (
                        <p>
                          {locale === 'zh-CN' ? '免费热线：' : 'Phone: '}
                          {siteAttrData.phone}
                        </p>
                      )}
                      {siteAttrData?.email && (
                        <p>
                          {locale === 'zh-CN' ? '邮箱：' : 'Email: '}
                          {siteAttrData.email}
                        </p>
                      )}
                    </div>
                  )}

                  {/* 关于lumii内容 */}
                  {(menu.label === '关于lumii' || menu.label === 'About Lumii') && (
                    <ul className="space-y-4 text-sm font-[300] text-white">
                      {menu.menus?.map((item: { id?: number; label?: string; url?: string }, itemIndex: number) => (
                        <li key={itemIndex}>
                          <Link
                            href={item.url || '#'}
                            className="transition-colors hover:text-white"
                          >
                            {item.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </motion.div>
              )}
            </div>
          ))}

          {/* 移动端社交媒体和二维码 */}
          <div className="mt-8 mb-6">
            {/* 社交媒体图标 */}
            {footerData?.socials && footerData.socials.length > 0 && (
              <div className="mb-6 flex justify-center space-x-10">
                {footerData.socials.map((social) => (
                  <Link
                    key={social.id}
                    href={social.url || '#'}
                    className="transition-opacity hover:opacity-80"
                  >
                    {social.icon && (
                      <Image
                        src={social.icon.url}
                        alt=""
                        width={0}
                        height={0}
                        sizes="100vw"
                        className="h-6 w-auto"
                      />
                    )}
                  </Link>
                ))}
              </div>
            )}

            {/* 二维码 */}
            {footerData?.qrCodes && footerData.qrCodes.length > 0 && (
              <div className="flex justify-center space-x-8">
                {footerData.qrCodes.map((qrCode) => (
                  <div key={qrCode.id} className="text-center">
                    {qrCode.image && (
                      <Image
                        src={qrCode.image.url}
                        alt={qrCode.label}
                        width={80}
                        height={80}
                        className="mb-2"
                      />
                    )}
                    <p className="text-xs text-gray-400">{qrCode.label}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* 底部版权信息 */}
        <div className="border-t border-[#666] pt-4 text-center">
          <p className="text-xs text-gray-500">{siteAttrData?.legal}</p>
        </div>
      </div>
    </footer>
  )
}
