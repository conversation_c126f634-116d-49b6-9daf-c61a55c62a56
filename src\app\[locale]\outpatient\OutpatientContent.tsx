'use client'

/**
 * 授权医生和技师页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import { useState } from 'react'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'
import Icon from '@/components/ui/Icon'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

// 扩展诊所信息类型
interface ClinicBlock extends StrapiBlock {
  __component?: string
  media?: Media
  buttons?: Button[]
  // 诊所特有字段
  name: string
  address: string
  phone: string
  schedule: string
}

interface OutpatientContentContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: ClinicBlock[]
  } | null
}

// 动画变体
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
}

const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

// 城市列表（可以从API获取或配置文件中读取）
const cities = ['全部', '杭州', '上海', '北京', '深圳', '广州', '成都', '武汉']

const BlockRenderer = ({ blocks }: { blocks: ClinicBlock[] }) => {
  const [selectedCity, setSelectedCity] = useState('全部')

  // 根据选中的城市过滤诊所
  const filteredBlocks =
    selectedCity === '全部'
      ? blocks
      : blocks.filter((block) => block.city === selectedCity || block.title?.includes(selectedCity))

  return (
    <div className="w-full">
      {/* 头部部分 */}
      <motion.div
        className="mb-8 flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* 左侧：总数量 */}
        <div className="flex items-center gap-2">
          <span className="text-lg font-medium text-gray-600">共</span>
          <span className="text-2xl font-bold text-[#D2A76A]">{blocks.length}</span>
          <span className="text-lg font-medium text-gray-600">家授权门诊</span>
        </div>

        {/* 右侧：城市下拉列表 */}
        <div className="relative">
          <select
            value={selectedCity}
            onChange={(e) => setSelectedCity(e.target.value)}
            className="appearance-none rounded-lg border border-gray-300 bg-white px-4 py-2 pr-8 text-sm font-medium text-gray-700 shadow-sm transition-colors hover:border-[#D2A76A] focus:border-[#D2A76A] focus:ring-2 focus:ring-[#D2A76A]/20 focus:outline-none"
          >
            {cities.map((city) => (
              <option key={city} value={city}>
                {city}
              </option>
            ))}
          </select>
          {/* 下拉箭头 */}
          <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
            <svg className="h-4 w-4 fill-current" viewBox="0 0 20 20">
              <path d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" />
            </svg>
          </div>
        </div>
      </motion.div>

      {/* 内容部分：诊所卡片列表 */}
      <motion.div
        className="space-y-6"
        variants={staggerContainer}
        initial="initial"
        animate="animate"
      >
        {filteredBlocks.map((block, index) => (
          <motion.div
            key={block.id || index}
            className="bg-white px-6 py-[16px] duration-300 hover:shadow-md"
            variants={fadeInUp}
            whileHover={{ y: -2 }}
            transition={{ duration: 0.2 }}
          >
            {/* 诊所名称*/}
            <div className="mb-5">
              <h3 className="text-[16px] leading-[22px] text-black">{block.name}</h3>
            </div>

            {/* 地址信息 */}
            {block.address && (
              <div className="flex items-center">
                <Icon name="weizhi" size="sm" />
                <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">
                  {block.address}
                </p>
              </div>
            )}
            {/* 诊所信息网格 */}
            <div className="mt-[14px] flex items-center">
              {/* 电话信息 */}
              {block.phone && (
                <div className="w-[50%] flex items-center">
                  <Icon name="dianhua" size="sm" />
                  <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">{block.phone}</p>
                </div>
              )}

              {/* 营业时间 */}
              {block.schedule && (
                <div className="w-[50%] flex items-center">
                  <Icon name="shijian" size="sm" />
                  <p className="ml-3 text-sm leading-[18px] font-[300] text-[#444]">{block.schedule}</p>
                </div>
              )}
            </div>
          </motion.div>
        ))}
      </motion.div>

      {/* 如果没有找到匹配的诊所 */}
      {filteredBlocks.length === 0 && (
        <motion.div
          className="py-12 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6 }}
        >
          <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <svg
              className="h-8 w-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900">暂无相关门诊</h3>
          <p className="text-sm text-gray-500">
            {selectedCity === '全部' ? '暂无门诊信息' : `在${selectedCity}暂无相关门诊`}
          </p>
        </motion.div>
      )}
    </div>
  )
}

// 主要的 OutpatientContentContent 组件
export default function OutpatientContentContent({ pageData }: OutpatientContentContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-0">
        <p>页面数据加载中...</p>
      </div>
    )
  }
  const { blocks } = pageData
  return (
    <div className="overflow-x-hidden overflow-y-visible bg-white pt-16 lg:bg-[#000] lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="overflow-x-hidden overflow-y-visible"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-10 left-7.5 z-10 w-full text-white lg:top-[40%] lg:left-[12%]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm leading-[28px] font-[300] lg:block lg:text-justify"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* 诊所列表 */}
      <motion.div
        className="overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="w-full bg-[#FAF6F2] pt-10 pb-5 lg:py-20">
          <div className="mx-auto px-4 lg:w-[76%] lg:px-0">
            <BlockRenderer blocks={blocks} />
          </div>
        </div>
      </motion.div>
    </div>
  )
}
