/**
 * Page API函数
 */

import type { IPage } from '@/types/strapi'
import { fetchStrapiCollection } from './client'
import { API_ENDPOINTS, buildPageFilteredParams, PAGE_POPULATE } from './config'


/**
 * 获取page页面数据
 */
export const getPage = async (locale: string, name: string): Promise<IPage | null> => {
  try {
    const response = await fetchStrapiCollection<IPage>(
      API_ENDPOINTS.PAGE,
      locale,
      buildPageFilteredParams({ name: { $eq: name } })
    )
    // 返回页面数据
    console.log(response)
    return response.data[0] || null
  } catch (error) {
    console.error('Failed to fetch page data:', error)
    return null
  }
}
