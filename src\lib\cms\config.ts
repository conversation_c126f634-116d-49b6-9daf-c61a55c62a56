/**
 * Strapi CMS 配置
 */

// 获取Strapi API基础URL
export const getStrapiUrl = (): string | undefined => {
  const url = process.env.STRAPI_API_URL
  return url?.endsWith('/') ? url?.slice(0, -1) : url
}

// 获取Strapi API Token
export const getStrapiToken = (): string | undefined => {
  return process.env.STRAPI_API_TOKEN
}

// API端点配置
export const API_ENDPOINTS = {
  ABOUT_US: '/api/about-us-pages',
  HEADER: '/api/headers',
  FOOTER: '/api/footers',
  SITE: '/api/sites',
  PAGE: '/api/pages',
} as const

// 公共站点过滤参数
export const SITE_FILTERS = {
  site: {
    name: {
      $eq: 'lumii',
    },
  },
} as const

// 默认查询参数
export const DEFAULT_POPULATE = {
  blocks: {
    populate: '*',
  },
  heroes: {
    populate: '*',
  },
} as const

// Footer查询参数 - 深度填充嵌套关联
export const FOOTER_POPULATE = {
  buttons: {
    populate: '*',
  },
  menus: {
    populate: '*',
  },
  qrCodes: {
    populate: {
      image: {
        populate: '*',
      },
    },
  },
  socials: {
    populate: {
      icon: {
        populate: '*',
      },
    },
  },
} as const

// Page查询参数 - 深度填充嵌套关联
export const buildPageFilteredParams = (additionalFilters?: Record<string, unknown>) => {
  return {
    filters: {
      ...SITE_FILTERS,
      ...additionalFilters,
    },
    heroes: {
      populate: '*',
    },
    blocks: {
      on: {
        'shared.block': {
          populate: '*',
        },
        'shared.deep-block': {
          populate: {
            buttons: {
              populate: '*',
            },
            blocks: {
              populate: '*',
            },
          },
        },
      },
    },
  }
}

export const PAGE_POPULATE = {
  filters: {
      ...SITE_FILTERS,
    },

  heroes: {
    populate: '*'
  },
  blocks: {
    on: {
      'shared.block': {
        populate: '*'
      },
      'shared.deep-block': {
        populate: {
          buttons: {
            populate: '*'
          },
          blocks: {
            populate: '*'
          }
        }
      }
    }
  }
} as const

/**
 * 构建带有站点过滤的查询参数
 */
export const buildSiteFilteredParams = (additionalFilters?: Record<string, unknown>) => {
  return {
    filters: {
      ...SITE_FILTERS,
      ...additionalFilters,
    },
    populate: DEFAULT_POPULATE,
    publicationState: 'live' as const,
  }
}
