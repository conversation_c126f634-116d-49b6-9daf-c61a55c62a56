'use client'

/**
 * 品牌页面内容组件 - 客户端组件
 */
import { motion } from 'motion/react'
import Image from 'next/image'
import type { StrapiHero, StrapiBlock } from '@/types/strapi'

// 定义媒体类型
interface Media {
  url: string
  alt?: string
}

// 定义按钮类型
interface Button {
  label: string
  url?: string
}

interface BrandContentProps {
  pageData: {
    title: string
    heroes: StrapiHero[]
    blocks: (StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] })[]
  } | null
}

// 动画配置
const staggerContainer = {
  animate: {
    transition: {
      staggerChildren: 0.1,
    },
  },
}

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
}

const scaleOnHover = {
  whileHover: { scale: 1.02 },
  transition: { duration: 0.3 },
}

// 区块组件
const BlockRenderer = ({
  block,
  index,
}: {
  block: StrapiBlock & { media?: Media; buttons?: Button[];},
  index: number
}) => {
  const { title, description, media } = block

  if (index === 0) {
    return (
      <motion.section
        className="px-4 py-10 lg:px-0 lg:py-20"
        initial={{ opacity: 0, y: 60 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true, amount: 0.3 }}
      >
        <motion.div
          className="flex flex-col"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* 上部：图片 */}
          <motion.div className="mb-5 lg:mb-12" variants={fadeInUp} transition={{ duration: 0.6 }}>
            {media && (
              <motion.div className="relative" {...scaleOnHover}>
                <Image
                  src={media.url}
                  alt={title || ''}
                  width={1200}
                  height={0}
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
          </motion.div>

          {/* 中部：标题 */}
          <motion.div
            className="text-center"
            variants={fadeInUp}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h2 className="mb-2 text-[20px] font-[500] lg:text-[22px]">{title}</h2>
          </motion.div>

          {/* 下部：描述文字 */}
          <motion.p
            className="line-clamp-responsive text-justify text-[14px] leading-[24px] text-[#444444]"
            variants={fadeInUp}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            {description}
          </motion.p>
        </motion.div>
      </motion.section>
    )
  }

  if (index === 1) {
    return (
      <motion.section
        className="px-4 py-10 lg:px-0 lg:py-20"
        initial={{ opacity: 0, y: 60 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        viewport={{ once: true, amount: 0.3 }}
      >
        <motion.div
          className="flex flex-col lg:flex-row lg:items-start lg:gap-20"
          variants={staggerContainer}
          initial="initial"
          whileInView="animate"
          viewport={{ once: true, amount: 0.3 }}
        >
          {/* 移动端：标题 */}
          <motion.div className="lg:hidden" variants={fadeInUp}>
            <h2 className="mb-3 text-[20px]">{title}</h2>
          </motion.div>

          {/* 左侧图片 */}
          <motion.div className="order-2 lg:order-1" variants={fadeInUp}>
            {media && (
              <motion.div {...scaleOnHover}>
                <Image
                  src={media.url}
                  alt={title || ''}
                  width={500}
                  height={320}
                  className="h-auto w-full object-cover lg:h-auto lg:w-full"
                />
              </motion.div>
            )}
          </motion.div>

          {/* 右侧内容 */}
          <motion.div
            className="order-1 flex h-full flex-col lg:order-2 lg:flex-1 lg:justify-between"
            variants={fadeInUp}
          >
            <div>
              {/* PC端：标题 */}
              <h2 className="mb-8 hidden text-4xl font-[30px] lg:block">{title}</h2>

              {/* 描述文字 */}
              <div className="line-clamp-responsive mb-7.5 text-sm leading-[28px] text-[#444444] lg:mb-0">
                {description}
              </div>
            </div>
          </motion.div>
        </motion.div>
      </motion.section>
    )
  }
  return null
}

// 主要的 BrandContent 组件
export default function BrandContent({ pageData }: BrandContentProps) {
  if (!pageData) {
    return (
      <div className="flex min-h-screen items-center justify-center pt-16 lg:pt-24">
        <p>页面数据加载中...</p>
      </div>
    )
  }

  return (
    <div className="overflow-x-hidden overflow-y-visible bg-[#000] pt-16 lg:pt-27">
      {/* Hero Banner */}
      <motion.div
        className="relative overflow-x-hidden overflow-y-visible"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
      >
        {pageData.heroes.map((item: StrapiHero) => (
          <motion.div
            key={item.id}
            className="overflow-x-hidden overflow-y-visible"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 2, ease: 'easeOut' }}
          >
            {item?.media?.url && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 1.2 }}
              >
                <Image
                  key={item.id}
                  src={item.media.url}
                  alt={item.title || ''}
                  width={1200}
                  height={800}
                  sizes="100vw"
                  className="h-auto w-full object-cover"
                />
              </motion.div>
            )}
            <motion.div
              className="absolute top-10 left-7.5 z-10 w-full text-[#fff] lg:top-[40%] lg:left-[12%]"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.5 }}
            >
              <div className="w-full lg:w-[28%]">
                <motion.h1
                  className="text-[20px] font-[500] lg:mb-8 lg:text-[36px]"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.7 }}
                >
                  {item.title}
                </motion.h1>
                <motion.div
                  className="hidden text-sm lg:block lg:text-justify font-[300] leading-[28px]"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.8, delay: 0.9 }}
                >
                  {item.description}
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        ))}
      </motion.div>

      {/* Blocks Content */}
      <motion.div
        className="overflow-x-hidden overflow-y-visible bg-[#FAF6F2]"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.3 }}
      >
        <div className="lg:mx-auto lg:w-[76%]">
          {pageData.blocks.map(
            (
              block: StrapiBlock & { __component?: string; media?: Media; buttons?: Button[] },
              index: number
            ) => (
              <motion.div
                key={block.id || index}
                initial={{ opacity: 0, y: 50 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true, amount: 0.2 }}
              >
                <BlockRenderer block={block} index={index} />
              </motion.div>
            )
          )}
        </div>
      </motion.div>
    </div>
  )
}
